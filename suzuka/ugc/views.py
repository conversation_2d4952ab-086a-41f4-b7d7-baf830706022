import logging

import jwt
import requests
from django.core.cache import cache
from django.http import (
    Http404,
    HttpRequest,
    HttpResponse,
    JsonResponse,
)
from django.shortcuts import get_object_or_404
from django.views.decorators.http import require_http_methods
from django.views.generic import TemplateView
from rest_framework import status

from suzuka.conf.sites import current_site
from suzuka.pages.context_processor import prepare_layout_context
from suzuka.pages.models import Page
from suzuka.pages.monaco import is_monaco_render
from suzuka.pages.utils import create_redirect_response, page_for_request
from suzuka.pagesapi.utils import get_decoded_data
from suzuka.ugc.models import UGCList
from suzuka.ugc.settings import UGC_LIST_MAX_ITEMS

from .forms import UgcFilterForm, UGCForm
from .utils import (
    get_category_by_content_type,
    get_organiser_by_user_email,
    get_ugc_by_id,
    get_user_by_email,
    save_ugc,
)

logger = logging.getLogger(__name__)

UGC_SECONDARY_PAGE_INFO = {
    "event": {"name": "What's On", "slug": "whats-on"},
    "photos": {"name": "Photos", "slug": "photos"},
    "story": {"name": "Local Experts", "slug": "local-experts"},
}


def validate_ugc_ownership(access_token: str, ugc_id: str) -> dict:
    """
    Validate that the user from the access token owns the specified UGC.
    """
    try:
        token_data = get_decoded_data(access_token)
        user_id = token_data.get("sub")

        if not user_id:
            return {
                "valid": False,
                "message": "Invalid token: no user ID found",
            }
    except jwt.InvalidTokenError as e:
        return {
            "valid": False,
            "message": f"Invalid token: {str(e)}",
        }
    except Exception as e:
        logger.error(f"Error decoding token: {e}")
        return {
            "valid": False,
            "message": "Token validation failed",
        }

    try:
        # Get the UGC data to check ownership
        ugc_data = get_ugc_by_id(int(ugc_id), use_cache=False, set_cache=False)

        if not ugc_data:
            return {
                "valid": False,
                "message": "UGC not found",
            }

        # Extract owner ID from UGC data
        # The UGC data should contain piano_user_id which identifies the owner
        owner_id = ugc_data.get("piano_user_id")

        if not owner_id:
            return {
                "valid": False,
                "message": "UGC owner information not found",
            }

        # Compare user ID from token with UGC owner ID
        if str(user_id) == str(owner_id):
            return {
                "valid": True,
                "message": "User is authorized to access this UGC",
                "user_id": user_id,
                "owner_id": owner_id,
            }
        else:
            return {
                "valid": False,
                "message": "User is not authorized to access this UGC",
                "user_id": user_id,
                "owner_id": owner_id,
            }

    except ValueError:
        return {
            "valid": False,
            "message": "Invalid UGC ID format",
        }
    except Exception as e:
        logger.error(f"Error validating UGC ownership: {e}")
        return {
            "valid": False,
            "message": "Ownership validation failed",
        }


class UGCView(TemplateView):
    page_name = ""
    page_url = ""

    def _is_allowed(self) -> bool:
        site = current_site()
        return site.settings.features.communityshareformfeature_enabled

    def _get_extra_context(self) -> dict:
        return {
            "page": Page(
                name=self.page_name,
                no_index=True,
                no_snippet=True,
                template=self.template_name,
                url=self.page_url,
            ),
            "view_type": "auth",
        }

    def get(self, request, *args, **kwargs):
        if not self._is_allowed():
            raise Http404()
        return super().get(request, *args, **kwargs)

    def render_to_response(self, context):
        if is_monaco_render():
            context.update(self._get_extra_context())
            context["full_page_render"] = True
            context["template_name"] = self.template_name
            context = prepare_layout_context(self.request, context)
            if context.get("redirect"):
                return create_redirect_response(context)
            return HttpResponse(context["rendered_react"])

        return super().render_to_response(context)


class ShareEventView(UGCView):
    template_name = "community/share_event.html"
    page_name = "Share Event"
    page_url = "share-event"


class SharePhotosView(UGCView):
    template_name = "community/share_photos.html"
    page_name = "Share Photos"
    page_url = "share-photos"


class ShareStoryView(UGCView):
    template_name = "community/share_story.html"
    page_name = "Share Story"
    page_url = "share-story"


def create_ugc_view(request: HttpRequest) -> JsonResponse:
    if request.method != "POST":
        return JsonResponse(
            {"success": False, "message": "Invalid request method."},
            status=405,
        )

    try:
        form = UGCForm(request.POST, files=request.FILES)
        if form.is_valid():
            api_response = save_ugc(request.FILES, form.cleaned_data)

            if api_response.get("success"):
                return JsonResponse(
                    {
                        "success": True,
                        "message": "Form is valid and UGC created.",
                    }
                )

            logger.warning(
                f"Failed to create UGC. API response: {api_response}"
            )
            return JsonResponse(
                {
                    "success": False,
                    "message": "Failed to create UGC.",
                    "errors": api_response.get("errors"),
                },
                status=500,
            )
        else:
            return JsonResponse(
                {"success": False, "errors": form.errors}, status=400
            )
    except requests.exceptions.RequestException as e:
        logger.warning(f"Create UGC request failed: {e}")
        return JsonResponse(
            {"success": False, "message": "Something went wrong."},
            status=500,
        )


def update_ugc_view(request: HttpRequest, ugc_id: str) -> JsonResponse:
    if request.method not in ("POST", "PATCH"):
        return JsonResponse(
            {"success": False, "message": "Invalid request method."},
            status=405,
        )

    # Validate ownership using access token
    access_token = request.headers.get("Authorization")
    if not access_token:
        return JsonResponse(
            {"success": False, "message": "Access token required."},
            status=401,
        )

    # Remove 'Bearer ' prefix if present
    if access_token.startswith("Bearer "):
        access_token = access_token[7:]

    validation_result = validate_ugc_ownership(access_token, ugc_id)
    if not validation_result["valid"]:
        return JsonResponse(
            {"success": False, "message": validation_result["message"]},
            status=403,
        )

    try:
        form = UGCForm(request.POST, files=request.FILES)
        if form.is_valid():
            api_response = save_ugc(
                request.FILES, form.cleaned_data, ugc_id=ugc_id
            )

            if api_response.get("success"):
                key = f"ugc-detail-{ugc_id}"
                cache.delete(key)
                return JsonResponse(
                    {
                        "success": True,
                        "message": "Form is valid and UGC updated.",
                    }
                )

            logger.warning(
                f"Failed to update UGC. API response: {api_response}"
            )
            return JsonResponse(
                {
                    "success": False,
                    "message": "Failed to update UGC.",
                    "errors": api_response.get("errors"),
                },
                status=500,
            )
        else:
            return JsonResponse(
                {"success": False, "errors": form.errors}, status=400
            )
    except requests.exceptions.RequestException as e:
        logger.warning(f"Update UGC request failed: {e}")
        return JsonResponse(
            {"success": False, "message": "Something went wrong."},
            status=500,
        )


def get_categories(request: HttpRequest) -> JsonResponse:
    content_type = request.GET.get("content_type")

    if not content_type:
        return JsonResponse(
            {"success": False, "message": "Content type is required."},
            status=400,
        )

    categories = get_category_by_content_type(content_type)

    if categories:
        return JsonResponse({"data": categories}, status=200)

    raise Http404


def get_organiser(request: HttpRequest) -> JsonResponse:
    email = request.GET.get("email")

    if not email:
        return JsonResponse(
            {"success": False, "message": "Email is required."},
            status=400,
        )

    organiser = get_organiser_by_user_email(email)

    if organiser:
        return JsonResponse({"data": organiser}, status=200)

    raise Http404


def get_user(request: HttpRequest) -> JsonResponse:
    email = request.GET.get("email")

    if not email:
        return JsonResponse(
            {"success": False, "message": "Email is required."},
            status=400,
        )

    user = get_user_by_email(email)

    if user:
        return JsonResponse({"data": user}, status=200)

    raise Http404


@require_http_methods(["GET"])
def ugc_items(request: HttpRequest, ugclist_id: int):
    """
    Return ugc items belonging to a ugclist.
    """
    form = UgcFilterForm(request.GET)
    if not form.is_valid():
        return JsonResponse(
            {"errors": form.errors}, status=status.HTTP_400_BAD_REQUEST
        )

    limit = form.cleaned_data.get("limit", UGC_LIST_MAX_ITEMS)
    offset = form.cleaned_data.get("offset", 0)
    site_id = form.cleaned_data["site_id"]
    show_all_sites = form.cleaned_data.get("show_all_sites", False)

    filters = {"publishable": True}

    optional_filters = ["date_range", "location", "category", "distance_km"]
    for field in optional_filters:
        if value := form.cleaned_data.get(field):
            filters[field] = value

    use_cache = request.GET.get("use_cache") != "false"
    show_region_param = request.GET.get("show_region")
    pinned_ugc_only = request.GET.get("pinned_ugc_only") or "false"
    show_region = (
        current_site().settings.ugcfeature_use_region
        if show_region_param is None
        else show_region_param.lower() == "true"
    )

    pinned_filters = filters.copy()
    ugc_list = get_object_or_404(UGCList, id=ugclist_id)
    ugc_items = ugc_list.ugc(
        filters=filters,
        pinned_filters=pinned_filters,
        use_cache=use_cache,
        site_id=site_id,
        ids_only=True
        if pinned_ugc_only.lower() == "true"
        else ugc_list.pinned_ugc_only,
        show_region=show_region,
        show_all_sites=show_all_sites,
    )

    result = list(ugc_items[offset : offset + limit])

    return JsonResponse(result, safe=False)


def ugc_detail(request, pk):
    """Show details of a single User Generated Content."""
    site = current_site()
    if not site.settings.features.communityshareformfeature_enabled:
        raise Http404

    ugc = get_ugc_by_id(int(pk), use_cache=False, set_cache=False)

    if not ugc or ugc["status"] != "approved":
        raise Http404

    root_path = request.path.strip("/").split("/")[0]
    suzuka_page = page_for_request(request, root_path)
    suzuka_page.template = "community/ugc_detail.html"
    secondary_page_info = UGC_SECONDARY_PAGE_INFO[ugc["content_type"]]
    context = {
        "ugc": ugc,
        "full_page_render": True,
        "page": suzuka_page,
        "pages": {
            "primary": {
                "name": suzuka_page.name,
                "url": root_path,
            },
            "secondary": {
                "name": secondary_page_info["name"],
                "url": f"/{root_path}/{secondary_page_info['slug']}/",
            },
        },
    }
    context = prepare_layout_context(request, context)
    if context.get("redirect"):
        return create_redirect_response(context)
    return HttpResponse(context["rendered_react"])


def ugc_edit(request, pk):
    """Edit a single User Generated Content."""
    site = current_site()
    if not site.settings.features.communityshareformfeature_enabled:
        raise Http404

    ugc = get_ugc_by_id(int(pk), use_cache=False, set_cache=False)

    if not ugc:
        raise Http404

    root_path = request.path.strip("/").split("/")[0]
    suzuka_page = page_for_request(request, root_path)
    suzuka_page.template = "community/ugc_edit.html"
    secondary_page_info = UGC_SECONDARY_PAGE_INFO[ugc["content_type"]]
    context = {
        "ugc": ugc,
        "full_page_render": True,
        "page": suzuka_page,
        "pages": {
            "primary": {
                "name": suzuka_page.name,
                "url": root_path,
            },
            "secondary": {
                "name": secondary_page_info["name"],
                "url": f"/{root_path}/{secondary_page_info['slug']}/",
            },
        },
    }
    context = prepare_layout_context(request, context)
    if context.get("redirect"):
        return create_redirect_response(context)
    return HttpResponse(context["rendered_react"])


@require_http_methods(["POST"])
def validate_ugc_ownership_view(request: HttpRequest) -> JsonResponse:
    """
    Standalone view to validate UGC ownership.

    Expects JSON payload with:
    - access_token: JWT access token
    - ugc_id: ID of the UGC to validate

    Returns JSON response with validation result.
    """
    try:
        import json

        data = json.loads(request.body)
        access_token = data.get("access_token")
        ugc_id = data.get("ugc_id")

        if not access_token:
            return JsonResponse(
                {"success": False, "message": "Access token is required."},
                status=400,
            )

        if not ugc_id:
            return JsonResponse(
                {"success": False, "message": "UGC ID is required."},
                status=400,
            )

        validation_result = validate_ugc_ownership(access_token, ugc_id)

        if validation_result["valid"]:
            return JsonResponse(
                {
                    "success": True,
                    "message": validation_result["message"],
                    "user_id": validation_result.get("user_id"),
                    "owner_id": validation_result.get("owner_id"),
                }
            )
        else:
            return JsonResponse(
                {
                    "success": False,
                    "message": validation_result["message"],
                    "user_id": validation_result.get("user_id"),
                    "owner_id": validation_result.get("owner_id"),
                },
                status=403,
            )

    except Exception as e:
        logger.error(f"Error in validate_ugc_ownership_view: {e}")
        return JsonResponse(
            {"success": False, "message": "Validation request failed."},
            status=500,
        )
