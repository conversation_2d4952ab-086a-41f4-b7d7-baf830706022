"""Tests for UGC views and validators."""

import json
from unittest.mock import Mock, patch

import jwt
from django.test import TestCase, RequestFactory
from django.http import JsonResponse

from .views import validate_ugc_ownership, validate_ugc_ownership_view


class TestUGCOwnershipValidator(TestCase):
    """Test cases for UGC ownership validation."""

    def setUp(self):
        self.factory = RequestFactory()
        self.valid_token = "valid_test_token"
        self.invalid_token = "invalid_test_token"
        self.ugc_id = "123"
        self.user_id = "user_123"

    @patch('suzuka.ugc.views.get_decoded_data')
    @patch('suzuka.ugc.views.get_ugc_by_id')
    def test_validate_ugc_ownership_success(self, mock_get_ugc, mock_decode_token):
        """Test successful ownership validation."""
        # Mock token decoding
        mock_decode_token.return_value = {"sub": self.user_id}

        # Mock UGC data
        mock_get_ugc.return_value = {"piano_user_id": self.user_id}

        result = validate_ugc_ownership(self.valid_token, self.ugc_id)

        self.assertTrue(result["valid"])
        self.assertEqual(result["user_id"], self.user_id)
        self.assertEqual(result["owner_id"], self.user_id)
        self.assertIn("authorized", result["message"])

    @patch('suzuka.ugc.views.get_decoded_data')
    @patch('suzuka.ugc.views.get_ugc_by_id')
    def test_validate_ugc_ownership_unauthorized(self, mock_get_ugc, mock_decode_token):
        """Test ownership validation with different user."""
        # Mock token decoding
        mock_decode_token.return_value = {"sub": "different_user"}

        # Mock UGC data
        mock_get_ugc.return_value = {"piano_user_id": self.user_id}

        result = validate_ugc_ownership(self.valid_token, self.ugc_id)

        self.assertFalse(result["valid"])
        self.assertEqual(result["user_id"], "different_user")
        self.assertEqual(result["owner_id"], self.user_id)
        self.assertIn("not authorized", result["message"])

    @patch('suzuka.ugc.views.get_decoded_data')
    def test_validate_ugc_ownership_invalid_token(self, mock_decode_token):
        """Test validation with invalid token."""
        mock_decode_token.side_effect = jwt.InvalidTokenError("Invalid token")

        result = validate_ugc_ownership(self.invalid_token, self.ugc_id)

        self.assertFalse(result["valid"])
        self.assertIn("Invalid token", result["message"])

    @patch('suzuka.ugc.views.get_decoded_data')
    @patch('suzuka.ugc.views.get_ugc_by_id')
    def test_validate_ugc_ownership_ugc_not_found(self, mock_get_ugc, mock_decode_token):
        """Test validation when UGC doesn't exist."""
        # Mock token decoding
        mock_decode_token.return_value = {"sub": self.user_id}

        # Mock UGC not found
        mock_get_ugc.return_value = None

        result = validate_ugc_ownership(self.valid_token, self.ugc_id)

        self.assertFalse(result["valid"])
        self.assertIn("UGC not found", result["message"])

    @patch('suzuka.ugc.views.get_decoded_data')
    @patch('suzuka.ugc.views.get_ugc_by_id')
    def test_validate_ugc_ownership_no_owner_info(self, mock_get_ugc, mock_decode_token):
        """Test validation when UGC has no owner information."""
        # Mock token decoding
        mock_decode_token.return_value = {"sub": self.user_id}

        # Mock UGC without owner info
        mock_get_ugc.return_value = {"title": "Test UGC"}

        result = validate_ugc_ownership(self.valid_token, self.ugc_id)

        self.assertFalse(result["valid"])
        self.assertIn("owner information not found", result["message"])

    def test_validate_ugc_ownership_invalid_ugc_id(self):
        """Test validation with invalid UGC ID format."""
        result = validate_ugc_ownership(self.valid_token, "invalid_id")

        # This should still work as we convert to int, but let's test with a truly invalid format
        # The actual validation happens in get_ugc_by_id, so this test ensures our error handling works
        self.assertIsInstance(result, dict)
        self.assertIn("valid", result)


class TestUGCOwnershipValidatorView(TestCase):
    """Test cases for the UGC ownership validation view."""

    def setUp(self):
        self.factory = RequestFactory()
        self.valid_payload = {
            "access_token": "valid_token",
            "ugc_id": "123"
        }

    @patch('suzuka.ugc.views.validate_ugc_ownership')
    def test_validate_ugc_ownership_view_success(self, mock_validate):
        """Test successful validation via view."""
        mock_validate.return_value = {
            "valid": True,
            "message": "User is authorized",
            "user_id": "user_123",
            "owner_id": "user_123"
        }

        request = self.factory.post(
            '/ugc/validate-ownership/',
            data=json.dumps(self.valid_payload),
            content_type='application/json'
        )

        response = validate_ugc_ownership_view(request)

        self.assertIsInstance(response, JsonResponse)
        self.assertEqual(response.status_code, 200)

        response_data = json.loads(response.content)
        self.assertTrue(response_data["success"])
        self.assertEqual(response_data["user_id"], "user_123")

    @patch('suzuka.ugc.views.validate_ugc_ownership')
    def test_validate_ugc_ownership_view_unauthorized(self, mock_validate):
        """Test unauthorized validation via view."""
        mock_validate.return_value = {
            "valid": False,
            "message": "User is not authorized",
            "user_id": "user_123",
            "owner_id": "user_456"
        }

        request = self.factory.post(
            '/ugc/validate-ownership/',
            data=json.dumps(self.valid_payload),
            content_type='application/json'
        )

        response = validate_ugc_ownership_view(request)

        self.assertIsInstance(response, JsonResponse)
        self.assertEqual(response.status_code, 403)

        response_data = json.loads(response.content)
        self.assertFalse(response_data["success"])

    def test_validate_ugc_ownership_view_missing_token(self):
        """Test view with missing access token."""
        payload = {"ugc_id": "123"}

        request = self.factory.post(
            '/ugc/validate-ownership/',
            data=json.dumps(payload),
            content_type='application/json'
        )

        response = validate_ugc_ownership_view(request)

        self.assertEqual(response.status_code, 400)
        response_data = json.loads(response.content)
        self.assertFalse(response_data["success"])
        self.assertIn("Access token is required", response_data["message"])

    def test_validate_ugc_ownership_view_missing_ugc_id(self):
        """Test view with missing UGC ID."""
        payload = {"access_token": "valid_token"}

        request = self.factory.post(
            '/ugc/validate-ownership/',
            data=json.dumps(payload),
            content_type='application/json'
        )

        response = validate_ugc_ownership_view(request)

        self.assertEqual(response.status_code, 400)
        response_data = json.loads(response.content)
        self.assertFalse(response_data["success"])
        self.assertIn("UGC ID is required", response_data["message"])

    def test_validate_ugc_ownership_view_invalid_json(self):
        """Test view with invalid JSON payload."""
        request = self.factory.post(
            '/ugc/validate-ownership/',
            data="invalid json",
            content_type='application/json'
        )

        response = validate_ugc_ownership_view(request)

        self.assertEqual(response.status_code, 400)
        response_data = json.loads(response.content)
        self.assertFalse(response_data["success"])
        self.assertIn("Invalid JSON", response_data["message"])