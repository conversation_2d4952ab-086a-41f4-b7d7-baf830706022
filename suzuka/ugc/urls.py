from django.urls import re_path
from django.views.generic.base import RedirectView

from . import feeds, traffic, views

urlpatterns = [
    re_path(
        r"^notice-board/share-event/$",
        views.ShareEventView.as_view(),
        name="share_event",
    ),
    re_path(
        r"^community/share-event/$",
        RedirectView.as_view(url="/notice-board/share-event/", permanent=True),
    ),
    re_path(
        r"^notice-board/share-photos/$",
        views.SharePhotosView.as_view(),
        name="share_photos",
    ),
    re_path(
        r"^community/share-photos/$",
        RedirectView.as_view(
            url="/notice-board/share-photos/", permanent=True
        ),
    ),
    re_path(
        r"^notice-board/share-story/$",
        views.ShareStoryView.as_view(),
        name="share_story",
    ),
    re_path(
        r"^notice-board/traffic/$",
        traffic.traffic,
        name="traffic",
    ),
    re_path(
        r"^notice-board/edit/(?P<pk>\d+)/[a-z0-9-]+/$",
        views.ugc_edit,
        name="ugc_edit",
    ),
    re_path(
        r"^notice-board/[a-z-]+/(?P<pk>\d+)/[a-z0-9-]+/$",
        views.ugc_detail,
        name="ugc_detail",
    ),
    re_path(
        r"^(?P<url>[a-z0-9/-]+)/rss-classifieds\.xml$",
        feeds.ClassifiedsFeed(),
        name="classifieds_feed",
    ),
    re_path(
        r"^(?P<url>[a-z0-9/-]+)/rss-tributes\.xml$",
        feeds.TributesFeed(),
        name="tributes_feed",
    ),
    re_path(
        # `ugc-` prefix avoids conflicts with other feeds
        r"^(?P<url>[a-z0-9/-]+)/rss-ugc-(?P<type>[a-zA-Z]+)\.xml$",
        feeds.UGCFeed(),
        name="ugc_feed",
    ),
    re_path(r"^ugc/create/$", views.create_ugc_view, name="create_ugc"),
    re_path(
        r"^ugc/update/(?P<ugc_id>[\w-]+)/$",
        views.update_ugc_view,
        name="update_ugc",
    ),
    re_path(
        r"^ugc/categories/$",
        views.get_categories,
        name="get_categories",
    ),
    re_path(
        r"^ugc/organiser/$",
        views.get_organiser,
        name="get_organiser",
    ),
    re_path(
        r"^ugc/user/$",
        views.get_user,
        name="get_user",
    ),
    re_path(
        r"^ugc/validate-ownership/$",
        views.validate_ugc_ownership_view,
        name="validate_ugc_ownership",
    ),
    re_path(
        r"^ugc/(?P<ugclist_id>\d+)/$", views.ugc_items, name="ugc_ugcitems"
    ),
]
